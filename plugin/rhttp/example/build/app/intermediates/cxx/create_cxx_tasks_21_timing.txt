# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 12ms
    create-variant-model 25ms
    create-ARMEABI_V7A-model 14ms
    create-X86_64-model 14ms
    [gap of 41ms]
  create-initial-cxx-model completed in 116ms
  [gap of 13ms]
create_cxx_tasks completed in 130ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 32ms
create_cxx_tasks completed in 38ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 35ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 31ms
create_cxx_tasks completed in 37ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 34ms
create_cxx_tasks completed in 42ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 19ms]
    create-X86-model 11ms
    [gap of 15ms]
  create-initial-cxx-model completed in 45ms
create_cxx_tasks completed in 51ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 32ms
  [gap of 11ms]
create_cxx_tasks completed in 44ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 33ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 31ms
create_cxx_tasks completed in 39ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 35ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 40ms
create_cxx_tasks completed in 49ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 30ms
create_cxx_tasks completed in 37ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 17ms]
    create-X86-model 18ms
    [gap of 19ms]
  create-initial-cxx-model completed in 54ms
create_cxx_tasks completed in 60ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 36ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 35ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 33ms
create_cxx_tasks completed in 40ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 36ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 35ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 32ms
create_cxx_tasks completed in 40ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 33ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 45ms
create_cxx_tasks completed in 51ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 29ms
create_cxx_tasks completed in 33ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 32ms
create_cxx_tasks completed in 40ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 29ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 30ms
create_cxx_tasks completed in 38ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 43ms
create_cxx_tasks completed in 52ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 24ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 33ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 22ms]
    create-ARMEABI_V7A-model 10ms
    [gap of 18ms]
  create-initial-cxx-model completed in 50ms
  [gap of 10ms]
create_cxx_tasks completed in 61ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 31ms
create_cxx_tasks completed in 36ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 24ms
create_cxx_tasks completed in 30ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 23ms
create_cxx_tasks completed in 29ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 44ms
  [gap of 10ms]
create_cxx_tasks completed in 54ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 29ms
create_cxx_tasks completed in 36ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 31ms
create_cxx_tasks completed in 40ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 31ms
create_cxx_tasks completed in 37ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 23ms
create_cxx_tasks completed in 29ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 35ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 42ms
create_cxx_tasks completed in 53ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 15ms]
    create-X86-model 20ms
    [gap of 12ms]
  create-initial-cxx-model completed in 47ms
create_cxx_tasks completed in 53ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 30ms
create_cxx_tasks completed in 36ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 36ms
create_cxx_tasks completed in 44ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 24ms
create_cxx_tasks completed in 30ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 35ms
create_cxx_tasks completed in 42ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 30ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 26ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 23ms]
    create-ARMEABI_V7A-model 20ms
  create-initial-cxx-model completed in 48ms
create_cxx_tasks completed in 55ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 30ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 24ms
create_cxx_tasks completed in 29ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 35ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 36ms
create_cxx_tasks completed in 41ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 33ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 35ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 24ms
create_cxx_tasks completed in 28ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 29ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 32ms
  [gap of 10ms]
create_cxx_tasks completed in 42ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 25ms
create_cxx_tasks completed in 30ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 32ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 31ms
create_cxx_tasks completed in 38ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 27ms
create_cxx_tasks completed in 34ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 33ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 29ms
create_cxx_tasks completed in 36ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 59ms
  [gap of 13ms]
create_cxx_tasks completed in 72ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 29ms
create_cxx_tasks completed in 31ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 40ms
create_cxx_tasks completed in 46ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 28ms
create_cxx_tasks completed in 35ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    [gap of 31ms]
    create-ARMEABI_V7A-model 12ms
  create-initial-cxx-model completed in 52ms
create_cxx_tasks completed in 61ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 37ms
  [gap of 13ms]
create_cxx_tasks completed in 50ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 34ms
  [gap of 19ms]
create_cxx_tasks completed in 53ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 29ms
create_cxx_tasks completed in 36ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model 29ms
create_cxx_tasks completed in 37ms

